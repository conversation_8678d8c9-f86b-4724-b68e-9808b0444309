<!DOCTYPE html>
<html>
<head>
    <title>Cable Selection Test - Like SubmarineCableMap.com</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />
    <style>
        body { 
            margin: 0; 
            padding: 0; 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        #map { 
            height: 100vh;
            width: 100%;
        }
        
        .cable-controls {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1001;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 6px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 15px;
            display: none;
        }
        
        .cable-controls.visible {
            display: block;
        }
        
        .show-all-btn {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .show-all-btn:hover {
            background: #c0392b;
            transform: translateY(-1px);
        }
        
        .selected-cable-info {
            margin-bottom: 10px;
            font-size: 14px;
            color: #2c3e50;
        }
        
        .selected-cable-name {
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .leaflet-interactive {
            cursor: pointer !important;
        }
        
        .leaflet-interactive:hover {
            filter: brightness(1.2);
        }
        
        .instructions {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1001;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 6px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 15px;
            max-width: 300px;
        }
        
        .instructions h3 {
            margin: 0 0 10px 0;
            color: #2c3e50;
            font-size: 16px;
        }
        
        .instructions p {
            margin: 0 0 10px 0;
            font-size: 14px;
            line-height: 1.4;
            color: #555;
        }
        
        .instructions ul {
            margin: 0;
            padding-left: 20px;
            font-size: 14px;
            color: #555;
        }
        
        .instructions li {
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <!-- Instructions -->
    <div class="instructions">
        <h3>🖱️ Cable Selection Test</h3>
        <p><strong>How to test:</strong></p>
        <ul>
            <li>Click on any cable line on the map</li>
            <li>All other cables will disappear completely</li>
            <li>Only the selected cable remains visible</li>
            <li>Use "Show All Cables" to restore view</li>
            <li>Hover over cables to see cursor change</li>
        </ul>
        <p><strong>Expected behavior:</strong> Exactly like submarinecablemap.com</p>
    </div>
    
    <!-- Cable Selection Controls -->
    <div id="cableControls" class="cable-controls">
        <div id="selectedCableInfo" class="selected-cable-info">
            <div id="selectedCableName" class="selected-cable-name"></div>
            <div id="selectedCableDetails"></div>
        </div>
        <button id="showAllBtn" class="show-all-btn">Show All Cables</button>
    </div>
    
    <div id="map"></div>
    
    <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
    <script>
        // Initialize the map
        const map = L.map('map').setView([20, 0], 2);
        
        L.tileLayer('https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png', {
            attribution: '© OpenStreetMap contributors © CARTO',
            subdomains: 'abcd',
            maxZoom: 19
        }).addTo(map);

        // Demo cables with realistic routes
        const demoCables = [
            { 
                id: '2africa',
                name: '2Africa', 
                color: '#e74c3c', 
                coords: [
                    [-34, -26], [-15, -8], [0, 5], [15, 20], [25, 35], [40, 30], [50, 25], [35, 40], [10, 45], [-5, 50], [-10, 55]
                ],
                details: 'Length: 45,000 km • RFS: 2023'
            },
            { 
                id: 'wacs',
                name: 'WACS', 
                color: '#3498db', 
                coords: [
                    [-34, -26], [-20, -10], [-15, 0], [-10, 10], [-5, 20], [0, 30], [5, 40], [-5, 50], [-10, 55]
                ],
                details: 'Length: 14,530 km • RFS: 2012'
            },
            { 
                id: 'ace',
                name: 'ACE', 
                color: '#f39c12', 
                coords: [
                    [-34, -26], [-25, -15], [-20, -5], [-15, 5], [-10, 15], [-5, 25], [0, 35], [5, 45], [-5, 50]
                ],
                details: 'Length: 17,000 km • RFS: 2012'
            },
            { 
                id: 'sat3',
                name: 'SAT-3/WASC', 
                color: '#9b59b6', 
                coords: [
                    [-34, -26], [-25, -12], [-18, 2], [-12, 12], [-8, 22], [-5, 32], [0, 42], [-8, 50]
                ],
                details: 'Length: 28,800 km • RFS: 2002'
            },
            { 
                id: 'mainone',
                name: 'MainOne', 
                color: '#27ae60', 
                coords: [
                    [5, 6], [0, 15], [-5, 25], [-8, 40]
                ],
                details: 'Length: 7,000 km • RFS: 2010'
            },
            { 
                id: 'seacom',
                name: 'SEACOM', 
                color: '#e67e22', 
                coords: [
                    [-34, -26], [20, -25], [35, -15], [45, 0], [50, 15], [40, 25]
                ],
                details: 'Length: 17,000 km • RFS: 2009'
            }
        ];

        const cableLayer = L.layerGroup().addTo(map);
        const cableLayers = [];
        let selectedCableId = null;
        let isCableSelected = false;
        let originalStyles = new Map();
        
        // Add demo cables to map
        demoCables.forEach((cable, index) => {
            const line = L.polyline(cable.coords, {
                color: cable.color,
                weight: 3,
                opacity: 0.8
            }).addTo(cableLayer);
            
            // Store original style
            originalStyles.set(cable.id, {
                color: cable.color,
                weight: 3,
                opacity: 0.8
            });
            
            line.bindPopup(`<b>${cable.name}</b><br>${cable.details}`);
            
            // Add click handler
            line.on('click', function(e) {
                handleCableClick(cable.id, cable.name, cable.details);
                L.DomEvent.stopPropagation(e);
            });
            
            // Add hover effects
            line.on('mouseover', function(e) {
                if (!isCableSelected || selectedCableId === cable.id) {
                    line.setStyle({
                        weight: 5,
                        opacity: Math.min(line.options.opacity + 0.3, 1.0)
                    });
                }
            });
            
            line.on('mouseout', function(e) {
                if (!isCableSelected || selectedCableId === cable.id) {
                    const originalStyle = originalStyles.get(cable.id);
                    line.setStyle({
                        weight: originalStyle.weight,
                        opacity: originalStyle.opacity
                    });
                }
            });
            
            cableLayers.push({ layer: line, cable: cable });
        });

        function handleCableClick(cableId, cableName, cableDetails) {
            console.log(`🖱️ Cable clicked: ${cableName} (ID: ${cableId})`);
            
            // If this cable is already selected, do nothing
            if (selectedCableId === cableId && isCableSelected) {
                return;
            }
            
            // Set selection state
            selectedCableId = cableId;
            isCableSelected = true;
            
            // Hide all cables except the selected one
            hideAllCablesExcept(cableId);
            
            // Show cable controls
            showCableControls(cableId, cableName, cableDetails);
            
            console.log(`✅ Cable selection active: Only "${cableName}" is visible`);
        }

        function hideAllCablesExcept(selectedCableId) {
            console.log(`👁️ Hiding all cables except: ${selectedCableId}`);
            
            let hiddenCount = 0;
            let visibleCount = 0;
            
            cableLayers.forEach(({ layer, cable }) => {
                if (cable.id === selectedCableId) {
                    // Keep selected cable fully visible with enhanced styling
                    const originalStyle = originalStyles.get(cable.id);
                    layer.setStyle({
                        color: originalStyle.color,
                        opacity: 1.0,
                        weight: originalStyle.weight + 1
                    });
                    layer.bringToFront();
                    visibleCount++;
                } else {
                    // Completely hide all other cables
                    layer.setStyle({
                        opacity: 0,
                        fillOpacity: 0
                    });
                    hiddenCount++;
                }
            });
            
            console.log(`📊 Cable visibility: ${visibleCount} visible, ${hiddenCount} hidden`);
        }

        function showAllCables() {
            console.log('🔄 Showing all cables');
            
            // Reset selection state
            selectedCableId = null;
            isCableSelected = false;
            
            // Restore all cables to original visibility
            cableLayers.forEach(({ layer, cable }) => {
                const originalStyle = originalStyles.get(cable.id);
                layer.setStyle({
                    color: originalStyle.color,
                    opacity: originalStyle.opacity,
                    weight: originalStyle.weight
                });
            });
            
            // Hide cable controls
            hideCableControls();
            
            console.log('✅ All cables restored to normal visibility');
        }

        function showCableControls(cableId, cableName, cableDetails) {
            const controls = document.getElementById('cableControls');
            const nameElement = document.getElementById('selectedCableName');
            const detailsElement = document.getElementById('selectedCableDetails');
            
            if (controls && nameElement && detailsElement) {
                nameElement.textContent = cableName;
                detailsElement.textContent = cableDetails;
                controls.classList.add('visible');
            }
        }

        function hideCableControls() {
            const controls = document.getElementById('cableControls');
            if (controls) {
                controls.classList.remove('visible');
            }
        }

        // Setup show all button
        document.getElementById('showAllBtn').addEventListener('click', showAllCables);

        console.log('🚀 Cable selection test loaded');
        console.log('💡 Click on any cable to see the selection effect');
        console.log('🎯 Expected: All other cables disappear completely (like submarinecablemap.com)');
    </script>
</body>
</html>
